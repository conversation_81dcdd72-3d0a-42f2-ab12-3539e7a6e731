/**
 * 客户端工具类
 */

/**
 * 获取客户端内网IP地址
 * @returns {Promise<string>} 客户端内网IP地址
 */
export function getClientIp() {
  return new Promise((resolve, reject) => {
    try {
      const rtc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      rtc.createDataChannel('');
      rtc.createOffer().then(offer => rtc.setLocalDescription(offer));

      const localIps = [];

      rtc.onicecandidate = function(event) {
        if (event.candidate) {
          const candidate = event.candidate.candidate;
          const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
          if (ipMatch && ipMatch[1] !== '127.0.0.1') {
            const ip = ipMatch[1];
            // 优先选择内网IP（192.168.x.x, 172.16-31.x.x, 10.x.x.x）
            if (isPrivateIP(ip)) {
              rtc.close();
              resolve(ip);
              return;
            } else {
              localIps.push(ip);
            }
          }
        }
      };

      // 超时处理
      setTimeout(() => {
        rtc.close();
        if (localIps.length > 0) {
          // 如果没有找到内网IP，使用第一个找到的IP
          resolve(localIps[0]);
        } else {
          console.warn('无法获取客户端IP地址，使用默认IP');
          resolve('127.0.0.1'); // 返回默认IP
        }
      }, 3000);

    } catch (error) {
      console.warn('WebRTC获取IP失败:', error);
      resolve('127.0.0.1'); // 返回默认IP
    }
  });
}

/**
 * 判断是否为内网IP
 * @param {string} ip IP地址
 * @returns {boolean} 是否为内网IP
 */
function isPrivateIP(ip) {
  const parts = ip.split('.').map(Number);

  // 10.0.0.0 - **************
  if (parts[0] === 10) {
    return true;
  }

  // ********** - **************
  if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) {
    return true;
  }

  // *********** - ***************
  if (parts[0] === 192 && parts[1] === 168) {
    return true;
  }

  return false;
}



/**
 * 获取客户端IP地址（静默模式）
 * 如果获取失败，返回默认IP而不抛出错误
 * @returns {Promise<string>} IP地址
 */
export function getClientIpSilent() {
  return getClientIp()
    .then(ip => ip)
    .catch(error => {
      console.warn('获取客户端IP失败，使用默认IP:', error.message);
      return '127.0.0.1';
    });
}

/**
 * 读取客户端本地JSON文件
 * @param {string} filePath 文件路径
 * @returns {Promise<Object>} 解析后的JSON对象
 */
export function readLocalJsonFile(filePath) {
  return new Promise((resolve, reject) => {
    // 检查是否支持File API
    if (!window.File || !window.FileReader) {
      reject(new Error('浏览器不支持File API'));
      return;
    }

    // 创建一个隐藏的文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.style.display = 'none';
    
    // 监听文件选择
    input.addEventListener('change', function(event) {
      const file = event.target.files[0];
      if (!file) {
        reject(new Error('未选择文件'));
        return;
      }

      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.json')) {
        reject(new Error('请选择JSON文件'));
        return;
      }

      // 读取文件内容
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const jsonContent = JSON.parse(e.target.result);
          resolve(jsonContent);
        } catch (error) {
          reject(new Error('JSON文件格式错误: ' + error.message));
        }
      };
      
      reader.onerror = function() {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsText(file);
    });

    // 触发文件选择对话框
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
  });
}

/**
 * 尝试读取指定路径的大门位置文件
 * 由于浏览器安全限制，无法直接读取指定路径的文件
 * 这个函数会提示用户手动选择文件
 * @param {string} expectedPath 期望的文件路径（仅用于提示）
 * @returns {Promise<Object>} 大门位置配置对象
 */
export function readGateLocationFile(expectedPath = 'C:\\Users\\<USER>\\GateLocation\\gate-locations.json') {
  return new Promise((resolve, reject) => {
    // 显示提示信息
    const message = `请选择大门位置配置文件：\n期望路径：${expectedPath}\n\n由于浏览器安全限制，需要您手动选择该文件。`;
    
    if (!confirm(message)) {
      reject(new Error('用户取消选择文件'));
      return;
    }

    // 调用文件读取函数
    readLocalJsonFile(expectedPath)
      .then(jsonContent => {
        // 验证文件格式
        if (!jsonContent.gates || !Array.isArray(jsonContent.gates)) {
          reject(new Error('大门位置文件格式错误：缺少gates数组'));
          return;
        }

        if (jsonContent.gates.length === 0) {
          reject(new Error('大门位置文件中没有大门信息'));
          return;
        }

        // 验证第一个大门信息的必要字段
        const firstGate = jsonContent.gates[0];
        if (!firstGate.code || !firstGate.name || firstGate.postag === undefined) {
          reject(new Error('大门位置信息不完整：缺少必要字段'));
          return;
        }

        resolve(jsonContent);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 自动读取大门位置文件（静默模式）
 * 如果读取失败，返回null而不是抛出错误
 * @returns {Promise<Object|null>} 大门位置配置对象或null
 */
export function readGateLocationFileSilent() {
  return readGateLocationFile()
    .then(content => content)
    .catch(error => {
      console.warn('读取大门位置文件失败:', error.message);
      return null;
    });
}
